<!DOCTYPE html>
<html lang="th">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      การเผยแพร่ประกาศการจัดซื้อจัดจ้างวิธีคัดเลือก และวิธีเฉพาะเจาะจง
    </title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      /* TH Sarabun New Font Face - Local Files */
      @font-face {
        font-family: "TH Sarabun New";
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url("fonts/THSarabunNew-Light.ttf") format("truetype");
      }

      @font-face {
        font-family: "TH Sarabun New";
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url("fonts/THSarabunNew-Regular.ttf") format("truetype");
      }

      @font-face {
        font-family: "TH Sarabun New";
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url("fonts/THSarabunNew-Medium.ttf") format("truetype");
      }

      @font-face {
        font-family: "TH Sarabun New";
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url("fonts/THSarabunNew-SemiBold.ttf") format("truetype");
      }

      @font-face {
        font-family: "TH Sarabun New";
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url("fonts/THSarabunNew-Bold.ttf") format("truetype");
      }

      body {
        font-family: "TH Sarabun New", "Sarabun", sans-serif !important;
        background-color: #f3f4f6;
        padding: 2rem;
        min-height: 100vh;
      }

      .step-card {
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .step-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      .hidden {
        display: none !important;
      }

      .slide-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
      }

      .slide-btn.prev {
        left: -25px;
      }
      .slide-btn.next {
        right: -25px;
      }

      .image-counter {
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
      }
    </style>
  </head>
  <body class="bg-gray-100 p-8 font-sans">
    <!-- หน้าการเผยแพร่ประกาศการจัดซื้อจัดจ้างวิธีคัดเลือก และวิธีเฉพาะเจาะจง -->
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-8 relative">
      <div class="text-center mb-8">
        <a
          href="index.html"
          class="absolute left-8 top-8 flex items-center gap-2 bg-gradient-to-r from-purple-100 to-blue-200 hover:from-blue-200 hover:to-purple-100 text-purple-700 hover:text-purple-900 px-4 py-2 rounded-full shadow transition-all duration-200 group"
        >
          <svg
            class="w-6 h-6 text-purple-500 group-hover:text-blue-600 transition"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 12l9-9 9 9M4 10v10a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V10"
            ></path>
          </svg>
          <span class="font-semibold hidden sm:inline">กลับหน้าแรก</span>
        </a>
        <h1 class="text-3xl font-bold text-purple-700">
          การเผยแพร่ประกาศการจัดซื้อจัดจ้างวิธีคัดเลือก <br />
          และวิธีเฉพาะเจาะจง
        </h1>
        <p class="text-lg text-gray-600 mt-2">
          โครงการที่ได้รับการยกเว้นการประกาศเผยแพร่แผนการจัดซื้อจัดจ้าง ตาม
          พ.ร.บ. จัดซื้อจัดจ้างฯ มาตรา 11
        </p>
      </div>

      <div class="mt-8 pt-8 border-t border-gray-200">
        <p class="text-xl font-bold text-purple-700 mb-6">
          ขั้นตอนการดำเนินการ
        </p>
      </div>

      <div class="space-y-6">
        <!-- ผู้ดำเนินการ -->
        <div
          class="bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-200 rounded-lg p-4 mb-6"
        >
          <h4 class="text-lg font-bold text-blue-800 mb-2">
            ผู้ดำเนินการ :
            <strong class="text-blue-700 font-medium">เจ้าหน้าที่</strong>
          </h4>
        </div>

        <div
          id="special-step1"
          class="step-card bg-blue-50 border-l-4 border-blue-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            1
          </div>
          <div>
            <h2 class="text-xl font-semibold text-blue-800">
              ล็อกอินเข้าระบบ e-Procurement
            </h2>
            <p class="text-gray-700 mt-2">
              เข้าสู่เว็บไซต์
              <a
                href="https://eprocurement.pea.co.th"
                class="text-blue-500"
                target="_blank"
                >https://eprocurement.pea.co.th</a
              >
              จากนั้นกรอกชื่อผู้ใช้งานและรหัสผ่านเพื่อล็อกอินเข้าระบบ
            </p>
          </div>
        </div>

        <div
          id="special-step2"
          class="step-card bg-green-50 border-l-4 border-green-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            2
          </div>
          <div>
            <h2 class="text-xl font-semibold text-green-800">
              เลือกการประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)
            </h2>
            <p class="text-gray-700 mt-2">
              เมื่อเข้าสู่ระบบได้แล้ว ให้คลิกที่เมนู "<strong
                class="font-medium"
                >การประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)</strong
              >"
            </p>
          </div>
        </div>

        <div
          id="special-step3"
          class="step-card bg-orange-50 border-l-4 border-orange-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-orange-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            3
          </div>
          <div>
            <h3 class="text-xl font-semibold text-orange-800">เพิ่มโครงการ</h3>
            <p class="text-gray-700 mt-2">
              เลือก 2. โครงการ --> เพิ่มโครงการ -->
              จากนั้นกรอกรายละเอียดในช่องที่มีสัญลักษณ์
              <span class="text-red-500 font-bold">*</span> ให้ครบถ้วน -->
              แล้วกดปุ่ม "<strong class="font-medium"
                >ยืนยันการเพิ่มโครงการ</strong
              >"
            </p>
          </div>
        </div>

        <div
          id="special-step4"
          class="step-card bg-cyan-50 border-l-4 border-cyan-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-cyan-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            4
          </div>
          <div>
            <h3 class="text-xl font-semibold text-cyan-800">
              ดำเนินการประกาศผลการพิจารณาการจัดซื้อจัดจ้าง (ผู้ชนะ)
            </h3>
            <p class="text-gray-700 mt-2">
              เลือก 6. ประกาศผลการพิจารณาการจัดซื้อจัดจ้าง (ผู้ชนะ) -->
              เพิ่มประกาศผลการพิจารณาฯ --> กดปุ่ม "เลือก Bid No" --> กดปุ่ม
              "ไปขั้นตอนถัดไป" --> จากนั้นกรอกรายละเอียดในช่องที่มีสัญลักษณ์
              <span class="text-red-500 font-bold">*</span> ให้ครบถ้วน -->
              แล้วกดปุ่ม "<strong class="font-medium">บันทีกและส่งเอกสาร</strong
              >"
            </p>
          </div>
        </div>

        <!-- ผู้ดำเนินการ -->
        <div class="mt-8 pt-8 border-t border-gray-200">
          <div
            class="bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-200 rounded-lg p-4 mb-6"
          >
            <h4 class="text-lg font-bold text-blue-800 mb-2">
              ผู้ดำเนินการ :
              <strong class="text-blue-700 font-medium"
                >ผู้อนุมัติประกาศ</strong
              >
            </h4>
          </div>
        </div>

        <div
          id="special-step5"
          class="step-card bg-gray-50 border-l-4 border-gray-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-gray-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            5
          </div>
          <div>
            <h3 class="text-xl font-semibold text-gray-800">
              ผู้อนุมัติประกาศดำเนินการ
            </h3>
            <p class="text-gray-700 mt-2">
              ผู้อนุมัติประกาศดำเนินการตามขั้นตอนที่ 1-2 แล้วไปที่เมนู "<strong
                class="font-medium"
                >6. ประกาศผลการพิจารณาการจัดซื้อจัดจ้าง (ผู้ชนะ)</strong
              >" --> "<strong class="font-medium">ถาดงานเข้า</strong>" -->
              "<strong class="font-medium">พิจารณาประกาศผลการพิจารณาฯ</strong>"
            </p>
          </div>
        </div>

        <div
          id="special-step6"
          class="step-card bg-pink-50 border-l-4 border-pink-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-pink-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            6
          </div>
          <div>
            <h3 class="text-xl font-semibold text-pink-800">
              อนุมัติและประกาศขึ้นเว็บไซต์
            </h3>
            <p class="text-gray-700 mt-2">
              เลือก "<strong class="font-medium">อนุมัติประกาศ</strong>"
              แล้วกดปุ่ม "<strong class="font-medium">ประกาศขึ้นเว็บไซต์</strong
              >"
            </p>
          </div>
        </div>

        
        <!-- ผู้ดำเนินการ -->
        <div class="mt-8 pt-8 border-t border-gray-200">
          <div
            class="bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-200 rounded-lg p-4 mb-6"
          >
            <h4 class="text-lg font-bold text-blue-800 mb-2">
              ผู้ดำเนินการ :
              <strong class="text-blue-700 font-medium">เจ้าหน้าที่</strong>
            </h4>
          </div>
        </div>

        <div
          id="special-step7"
          class="step-card bg-cyan-50 border-l-4 border-cyan-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-cyan-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            7
          </div>
          <div>
            <h3 class="text-xl font-semibold text-cyan-800">
              ดำเนินการประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง
            </h3>
            <p class="text-gray-700 mt-2">
              เลือก 9. ประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง -->
              เพิ่มประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง --> กดปุ่ม "เลือก Bid No" --> เลือก "ผู้ได้รับการคัดเลือก" --> กดปุ่ม
              "ไปขั้นตอนถัดไป" --> จากนั้นกรอกรายละเอียดในช่องที่มีสัญลักษณ์
              <span class="text-red-500 font-bold">*</span> ให้ครบถ้วน -->
              แล้วกดปุ่ม "<strong class="font-medium">บันทีกและส่งเอกสาร</strong
              >"
            </p>
          </div>
        </div>

        <!-- ผู้ดำเนินการ -->
        <div class="mt-8 pt-8 border-t border-gray-200">
          <div
            class="bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-200 rounded-lg p-4 mb-6"
          >
            <h4 class="text-lg font-bold text-blue-800 mb-2">
              ผู้ดำเนินการ :
              <strong class="text-blue-700 font-medium"
                >ผู้อนุมัติประกาศ</strong
              >
            </h4>
          </div>
        </div>

        <div
          id="special-step8"
          class="step-card bg-gray-50 border-l-4 border-gray-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-gray-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            8
          </div>
          <div>
            <h3 class="text-xl font-semibold text-gray-800">
              ผู้อนุมัติประกาศดำเนินการ
            </h3>
            <p class="text-gray-700 mt-2">
              ผู้อนุมัติประกาศดำเนินการตามขั้นตอนที่ 1-2 แล้วไปที่เมนู "<strong
                class="font-medium"
                >9. ประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง</strong
              >" --> "<strong class="font-medium">ถาดงานเข้า</strong>" -->
              "<strong class="font-medium">พิจารณาประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง</strong>"
            </p>
          </div>
        </div>

        <div
          id="special-step9"
          class="step-card bg-pink-50 border-l-4 border-pink-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-pink-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            9
          </div>
          <div>
            <h3 class="text-xl font-semibold text-pink-800">
              อนุมัติและประกาศขึ้นเว็บไซต์
            </h3>
            <p class="text-gray-700 mt-2">
              เลือก "<strong class="font-medium">อนุมัติประกาศ</strong>"
              แล้วกดปุ่ม "<strong class="font-medium">ประกาศขึ้นเว็บไซต์</strong
              >"
            </p>
          </div>
        </div>

        <!-- ผู้ดำเนินการ -->
        <div class="mt-8 pt-8 border-t border-gray-200">
          <div
            class="bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-200 rounded-lg p-4 mb-6"
          >
            <h4 class="text-lg font-bold text-blue-800 mb-2">
              ผู้ดำเนินการ :
              <strong class="text-blue-700 font-medium">เจ้าหน้าที่</strong>
            </h4>
          </div>
        </div>

        <div
          id="special-step10"
          class="step-card bg-cyan-50 border-l-4 border-cyan-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-cyan-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            10
          </div>
          <div>
            <h3 class="text-xl font-semibold text-cyan-800">
              ลงข้อมูลเบิกจ่ายเงินในแต่ละสัญญา
            </h3>
            <p class="text-gray-700 mt-2">
              เลือก 10. รายการบริหารสัญญา (เบิกจ่าย) -->เลือก "บันทึกข้อมูลเบิกจ่าย" --> ไปที่ บันทึกข้อมูลเบิกจ่าย --> กดปุ่มเพิ่ม --> จากนั้นกรอกรายละเอียดในช่องที่มีสัญลักษณ์
              <span class="text-red-500 font-bold">*</span> ให้ครบถ้วน --> กดปุ่ม "บันทึก" --> กดปุ่ม "บันทึก"
            </p>
          </div>
        </div>

      </div>
    </div>

    <!-- Popup สำหรับแสดงรูปภาพ -->
    <div
      id="popup-overlay"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden"
    >
      <div class="bg-white p-8 rounded-lg shadow-lg max-w-5xl w-full relative">
        <button
          id="close-popup"
          class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold"
        >
          &times;
        </button>
        <button
          id="fullscreen-btn"
          class="absolute top-3 right-16 text-gray-600 hover:text-gray-900 text-xl font-bold bg-white hover:bg-gray-100 rounded-full w-10 h-10 flex items-center justify-center shadow-md transition-all duration-200"
          title="ขยายเต็มหน้าจอ"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 3H5C3.89543 3 3 3.89543 3 5V8M21 8V5C21 3.89543 20.1046 3 19 3H16M16 21H19C20.1046 21 21 20.1046 21 19V16M3 16V19C3 20.1046 3.89543 21 5 21H8"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <h3 id="popup-title" class="text-2xl font-bold mb-4 text-center"></h3>
        <div class="flex items-center justify-center relative">
          <button id="prev-btn" class="slide-btn prev hidden">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 18L9 12L15 6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <img
            id="popup-image"
            src=""
            alt="Step Image"
            class="w-full h-auto mb-4 rounded shadow-lg"
          />
          <button id="next-btn" class="slide-btn next hidden">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9 18L15 12L9 6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <p id="popup-description" class="text-gray-800 text-center mb-4"></p>
        <div id="image-counter" class="flex justify-center hidden">
          <div class="image-counter">
            <span id="current-image">1</span> / <span id="total-images">1</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Full-screen overlay สำหรับแสดงรูปภาพเต็มหน้าจอ -->
    <div
      id="fullscreen-overlay"
      class="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center hidden z-50"
    >
      <div class="relative w-full h-full flex items-center justify-center">
        <button
          id="close-fullscreen"
          class="absolute top-4 right-4 text-white hover:text-gray-300 text-3xl font-bold z-10 bg-black bg-opacity-50 rounded-full w-12 h-12 flex items-center justify-center"
        >
          &times;
        </button>
        <button
          id="fullscreen-prev-btn"
          class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 text-2xl font-bold z-10 bg-black bg-opacity-50 rounded-full w-12 h-12 flex items-center justify-center hidden"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M15 18L9 12L15 6"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <img
          id="fullscreen-image"
          src=""
          alt="Full Screen Image"
          class="max-w-full max-h-full object-contain cursor-pointer"
        />
        <button
          id="fullscreen-next-btn"
          class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 text-2xl font-bold z-10 bg-black bg-opacity-50 rounded-full w-12 h-12 flex items-center justify-center hidden"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 18L15 12L9 6"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <div
          id="fullscreen-counter"
          class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black bg-opacity-50 px-4 py-2 rounded-full hidden"
        >
          <span id="fullscreen-current">1</span> /
          <span id="fullscreen-total">1</span>
        </div>
      </div>
    </div>

    <script>
      // JavaScript for Pop-up functionality
      const stepCards = document.querySelectorAll(".step-card");
      const popupOverlay = document.getElementById("popup-overlay");
      const popupTitle = document.getElementById("popup-title");
      const popupImage = document.getElementById("popup-image");
      const popupDescription = document.getElementById("popup-description");
      const closePopupBtn = document.getElementById("close-popup");
      const prevBtn = document.getElementById("prev-btn");
      const nextBtn = document.getElementById("next-btn");
      const imageCounter = document.getElementById("image-counter");
      const currentImageSpan = document.getElementById("current-image");
      const totalImagesSpan = document.getElementById("total-images");

      // Full-screen elements
      const fullscreenBtn = document.getElementById("fullscreen-btn");
      const fullscreenOverlay = document.getElementById("fullscreen-overlay");
      const fullscreenImage = document.getElementById("fullscreen-image");
      const closeFullscreenBtn = document.getElementById("close-fullscreen");
      const fullscreenPrevBtn = document.getElementById("fullscreen-prev-btn");
      const fullscreenNextBtn = document.getElementById("fullscreen-next-btn");
      const fullscreenCounter = document.getElementById("fullscreen-counter");
      const fullscreenCurrentSpan =
        document.getElementById("fullscreen-current");
      const fullscreenTotalSpan = document.getElementById("fullscreen-total");

      const stepData = {
        "special-step1": {
          title: "ล็อกอินเข้าระบบ e-Procurement",
          images: ["Image/001.png"],
          description: "หน้าจอเข้าระบบ e-Procurement",
        },
        "special-step2": {
          title: "เลือกการประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)",
          images: ["Image/002.png"],
          description: "หน้าจอเลือกการประกาศจัดซื้อจัดจ้าง",
        },
        "special-step3": {
          title: "การเพิ่มโครงการ",
          images: [
            "Image/special/003.png",
            "Image/special/003-1.png",
            "Image/special/003-2.png",
            "Image/special/003-3.png",
            "Image/special/003-4.png",
            "Image/special/003-5.png",
          ],
          description: "หน้าจอการเพิ่มโครงการ",
        },
        "special-step4": {
          title: "กรอกข้อมูลการประกาศ",
          images: [
            "Image/special/004-1.png",
            "Image/special/004-2.png",
            "Image/special/004-3.png",
            "Image/special/004-4.png",
            "Image/special/004-5.png",
            "Image/special/004-6.png",
            "Image/special/004-7.png",
            "Image/special/004-8.png",
            "Image/special/004-9.png",
            "Image/special/004-10.png",
          ],
          description: "หน้าจอกรอกรายละเอียดการประกาศเผยแพร่",
        },
        "special-step5": {
          title: "ผู้อนุมัติประกาศดำเนินการ",
          images: ["Image/special/005.png"],
          description: "หน้าจอถาดงานเข้าและพิจารณาการจัดซื้อจัดจ้าง (ผู้ชนะ)",
        },
        "special-step6": {
          title: "ประกาศเผยแพร่ผลการพิจารณาการจัดซื้อจัดจ้าง (ผู้ชนะ)",
          images: ["Image/special/006.png"],
          description: "หน้าจออนุมัติประกาศและประกาศขึ้นเว็บไซต์",
        },
        "special-step7": {
          title: "ประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง",
          images: [
            "Image/special/007-1.png",
            "Image/special/007-2.png",
            "Image/special/007-3.png",
            "Image/special/007-4.png",
            "Image/special/007-5.png",
          ],
          description: "หน้าจอกรอกประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง",
        },
        "special-step8": {
          title: "ผู้อนุมัติประกาศดำเนินการ",
          images: ["Image/special/008.png"],
          description: "หน้าจอถาดงานเข้าและพิจารณาประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง",
        },
        "special-step9": {
          title: "ประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง",
          images: ["Image/special/009.png"],
          description: "หน้าจออนุมัติประกาศและประกาศขึ้นเว็บไซต์",
        },
        "special-step10": {
          title: "ประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง",
          images: [
            "Image/special/007-1.png",
            "Image/special/007-2.png",
            "Image/special/007-3.png",
            "Image/special/007-4.png",
            "Image/special/007-5.png",
          ],
          description: "หน้าจอกรอกประกาศเผยแพร่สาระสำคัญของสัญญาหรือข้อตกลง",
        },
      };

      let currentStepImages = [];
      let currentImageIndex = 0;

      function updateImage() {
        if (currentStepImages.length > 0) {
          popupImage.src = currentStepImages[currentImageIndex];
          currentImageSpan.textContent = currentImageIndex + 1;
          totalImagesSpan.textContent = currentStepImages.length;

          if (currentStepImages.length >= 2) {
            prevBtn.classList.remove("hidden");
            nextBtn.classList.remove("hidden");
            imageCounter.classList.remove("hidden");

            prevBtn.style.opacity = currentImageIndex === 0 ? "0.5" : "1";
            nextBtn.style.opacity =
              currentImageIndex === currentStepImages.length - 1 ? "0.5" : "1";
          } else {
            prevBtn.classList.add("hidden");
            nextBtn.classList.add("hidden");
            imageCounter.classList.add("hidden");
          }
        }
      }

      stepCards.forEach((card) => {
        card.addEventListener("click", () => {
          const stepId = card.id;
          const stepInfo = stepData[stepId];

          if (stepInfo) {
            popupTitle.textContent = stepInfo.title;
            popupDescription.textContent = stepInfo.description;
            currentStepImages = stepInfo.images;
            currentImageIndex = 0;

            updateImage();
            popupOverlay.classList.remove("hidden");
          }
        });
      });

      prevBtn.addEventListener("click", () => {
        if (currentImageIndex > 0 && currentStepImages.length >= 2) {
          currentImageIndex--;
          updateImage();
        }
      });

      nextBtn.addEventListener("click", () => {
        if (
          currentImageIndex < currentStepImages.length - 1 &&
          currentStepImages.length >= 2
        ) {
          currentImageIndex++;
          updateImage();
        }
      });

      closePopupBtn.addEventListener("click", () => {
        popupOverlay.classList.add("hidden");
      });

      popupOverlay.addEventListener("click", (e) => {
        if (e.target === popupOverlay) {
          popupOverlay.classList.add("hidden");
        }
      });

      // Full-screen functionality
      fullscreenBtn.addEventListener("click", () => {
        fullscreenImage.src = popupImage.src;
        updateFullscreenNavigation();
        fullscreenOverlay.classList.remove("hidden");
      });

      function updateFullscreenNavigation() {
        if (currentStepImages.length >= 2) {
          fullscreenPrevBtn.classList.remove("hidden");
          fullscreenNextBtn.classList.remove("hidden");
          fullscreenCounter.classList.remove("hidden");
          fullscreenTotalSpan.textContent = currentStepImages.length;
          fullscreenCurrentSpan.textContent = currentImageIndex + 1;

          fullscreenPrevBtn.style.opacity =
            currentImageIndex === 0 ? "0.5" : "1";
          fullscreenNextBtn.style.opacity =
            currentImageIndex === currentStepImages.length - 1 ? "0.5" : "1";
        } else {
          fullscreenPrevBtn.classList.add("hidden");
          fullscreenNextBtn.classList.add("hidden");
          fullscreenCounter.classList.add("hidden");
        }
      }

      fullscreenPrevBtn.addEventListener("click", () => {
        if (currentImageIndex > 0 && currentStepImages.length >= 2) {
          currentImageIndex--;
          fullscreenImage.src = currentStepImages[currentImageIndex];
          updateImage();
          updateFullscreenNavigation();
        }
      });

      fullscreenNextBtn.addEventListener("click", () => {
        if (
          currentImageIndex < currentStepImages.length - 1 &&
          currentStepImages.length >= 2
        ) {
          currentImageIndex++;
          fullscreenImage.src = currentStepImages[currentImageIndex];
          updateImage();
          updateFullscreenNavigation();
        }
      });

      closeFullscreenBtn.addEventListener("click", () => {
        fullscreenOverlay.classList.add("hidden");
      });

      fullscreenOverlay.addEventListener("click", (e) => {
        if (e.target === fullscreenOverlay) {
          fullscreenOverlay.classList.add("hidden");
        }
      });

      document.addEventListener("keydown", (e) => {
        if (
          e.key === "Escape" &&
          !fullscreenOverlay.classList.contains("hidden")
        ) {
          fullscreenOverlay.classList.add("hidden");
        }
      });

      popupImage.addEventListener("click", () => {
        fullscreenBtn.click();
      });
    </script>
  </body>
</html>
